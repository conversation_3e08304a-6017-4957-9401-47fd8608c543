<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="6" failures="0" errors="0" time="5.058">
  <testsuite name="End-to-End Order Flow Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-09T00:18:12" time="4.633" tests="6">
    <testcase classname="End-to-End Order Flow Tests GraphQL Schema Tests should have working GraphQL endpoint" name="End-to-End Order Flow Tests GraphQL Schema Tests should have working GraphQL endpoint" time="0.274">
    </testcase>
    <testcase classname="End-to-End Order Flow Tests GraphQL Schema Tests should have order-related types in schema" name="End-to-End Order Flow Tests GraphQL Schema Tests should have order-related types in schema" time="0.03">
    </testcase>
    <testcase classname="End-to-End Order Flow Tests GraphQL Schema Tests should handle health check endpoint" name="End-to-End Order Flow Tests GraphQL Schema Tests should handle health check endpoint" time="0.03">
    </testcase>
    <testcase classname="End-to-End Order Flow Tests Error Scenarios should handle invalid GraphQL queries" name="End-to-End Order Flow Tests Error Scenarios should handle invalid GraphQL queries" time="0.045">
    </testcase>
    <testcase classname="End-to-End Order Flow Tests Error Scenarios should handle unauthorized GraphQL access" name="End-to-End Order Flow Tests Error Scenarios should handle unauthorized GraphQL access" time="0.028">
    </testcase>
    <testcase classname="End-to-End Order Flow Tests Error Scenarios should handle invalid restaurant ID in GraphQL" name="End-to-End Order Flow Tests Error Scenarios should handle invalid restaurant ID in GraphQL" time="0.023">
    </testcase>
  </testsuite>
</testsuites>